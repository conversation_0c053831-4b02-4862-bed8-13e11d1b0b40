import { defineClientConfig } from "vuepress/client";
import { setupTransparentNavbar } from "vuepress-theme-hope/presets/transparentNavbar.js";
import "vuepress-theme-hope/presets/round-blogger-avatar.scss";
// import { setupRunningTimeFooter } from "vuepress-theme-hope/presets/footerRunningTime.js";

export default defineClientConfig({
  setup: () => {
    setupTransparentNavbar({ type: "homepage" });
    // setupRunningTimeFooter(
    //   new Date("2021-07-30"),
    //   {
    //     "/": "本站已运行 :day 天 :hour 小时 :minute 分钟 :second 秒",
    //   },
    //   true
    // );
  },
});
