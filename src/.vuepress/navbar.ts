import { navbar } from "vuepress-theme-hope";

export default navbar([
  {
    text: "博客主页",
    icon: "noto-v1:house",
    link: "/",
  },
  {
    text: "前端相关",
    icon: "material-icon-theme:vue",
    prefix: "/frontEnd/",
    children: [
      {
        text: "前端笔记",
        icon: "streamline-flex-color:notepad-text-flat",
        link: "notes/",
      },
      {
        text: "前端文档",
        icon: "icon-park:document-folder",
        link: "documents/",
      }
    ],
  },
  {
    text:"Linux",
    icon: "logos:linux-tux",
    link: "/linux/",
  },
  {
    text: "网络技术",
    icon: "streamline-ultimate-color:amazon-web-services-logo",
    link: "/networkTech/",
  },
  {
    text: "生活随笔",
    icon: "noto-v1:notebook",
    link: "/life/",
  },
  {
    text: "关于本站",
    icon: "fluent-emoji-flat:man-curly-hair-light",
    link: "/intro",
  },
]);

// export default navbar([
//   "/",
//   "/demo/",
//   {
//     text: "博文",
//     icon: "pen-to-square",
//     prefix: "/posts/",
//     children: [
//       {
//         text: "苹果",
//         icon: "pen-to-square",
//         prefix: "apple/",
//         children: [
//           { text: "苹果1", icon: "pen-to-square", link: "1" },
//           { text: "苹果2", icon: "pen-to-square", link: "2" },
//           "3",
//           "4",
//         ],
//       },
//       {
//         text: "香蕉",
//         icon: "pen-to-square",
//         prefix: "banana/",
//         children: [
//           {
//             text: "香蕉 1",
//             icon: "pen-to-square",
//             link: "1",
//           },
//           {
//             text: "香蕉 2",
//             icon: "pen-to-square",
//             link: "2",
//           },
//           "3",
//           "4",
//         ],
//       },
//       { text: "樱桃", icon: "pen-to-square", link: "cherry" },
//       { text: "火龙果", icon: "pen-to-square", link: "dragonfruit" },
//       "tomato",
//       "strawberry",
//     ],
//   },
//   {
//     text: "V2 文档",
//     icon: "book",
//     link: "https://theme-hope.vuejs.press/zh/",
//   },
// ]);
