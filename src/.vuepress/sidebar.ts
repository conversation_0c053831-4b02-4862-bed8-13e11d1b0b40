import { sidebar } from "vuepress-theme-hope";

export default sidebar({
  "/frontEnd/notes/": [
    {
      text: "前端笔记",
      icon: "streamline-flex-color:notepad-text-flat",
      prefix: "",
      children: "structure",
    },
  ],
  "/frontEnd/documents/": [
    {
      text: "前端文档",
      icon: "icon-park:document-folder",
      prefix: "",
      children: "structure",
    },
  ],
  "/linux/": [
    {
      text: "Linux",
      icon: "logos:linux-tux",
      prefix: "",
      children: "structure",
    },
  ],
  "/networkTech/": [
    {
      text: "网络技术",
      icon: "streamline-ultimate-color:amazon-web-services-logo",
      prefix: "",
      children: "structure",
    },
  ],
  "/life/": [
    {
      text: "生活随笔",
      icon: "noto-v1:notebook",
      prefix: "",
      children: "structure",
    },
  ],
});
