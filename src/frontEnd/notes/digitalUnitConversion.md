---
title: js数字单位转化
date: 2020-11-29
categories:
 - 前端笔记
# tags:
---

最近在做一个音乐小程序，歌单部分需要展示播放量，数字过大后会以单位'万'或'亿'显示，效果参考网易云：

![](https://piccos.yangqifan.top/digitalUnitConversion1.png?imageSlim)

方法如下：

```js
    _tranNumber(num,point) {	//num表示传入的数字  point表示小数点位数
      let numStr = num.toString().split('.')[0]	//先将数字转换成字符串，取整数部分
      // 十万以内直接返回 
      if (numStr.length < 6) {
        return numStr;
      }
      else if (numStr.length >= 6 && numStr.length<=8) {
        let decimal = numStr.substring(numStr.length - 4, numStr.length - 4 + point)
        return parseFloat(parseInt(num / 10000) + '.' + decimal) + '万'
      }
      else if (numStr.length > 8) {
        let decimal = numStr.substring(numStr.length - 8, numStr.length - 8 + point)
        return parseFloat(parseInt(num / 100000000) + '.' + decimal) + '亿'
      }
    }
```

转换后的结果：

![](https://piccos.yangqifan.top/digitalUnitConversion2.png?imageSlim)