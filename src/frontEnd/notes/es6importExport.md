---
title: ES6模块导入和导出
date: 2021-11-26
categories:
 - 前端笔记
tags:
 - 按需导入
 - 按需导出
 - 默认导入
 - 默认导出
 - es6
---

### ES6模块化规范

- 每个js文件都是一个独立的模块
- 导入其他模块成员使用**import**关键字
- 向外共享模块成员使用**export**关键字

### 默认导出

```js
// export default 默认导出的成员

let n1 = 10;

let n2 = 20;

function show() {}

export default { n1, show };
```

- 每个模块中，只允许使用一次export default，否则会报错

### 默认导入

```js
// import 接收名称 from '模块标识符'

import xy from './demo.js'

console.log(xy)  //打印结果：{ n1: 10, show: [Function: show] }
```

- 默认导入的接收名称可以是任意合法的名称

### 按需导出

```js
export let n1 = 10

export let s2 = 'aaa'

export function say() {}
```

### 按需导入

```js
import {n1,s2 as str2,say} from './demo.js'

console.log(n1)	// 10
console.log(str2)	// aaa
console.log(say) // [Function: say]
```

### 直接导入并执行模块中的代码

```js
for (let i = 0; i < 3; i++) {
  console.log(i);
}

--------------------------------------------

import './test1.js' //直接导入并执行
```


