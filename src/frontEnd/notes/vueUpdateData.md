---
title: VUE中更新数据页面视图无法改变的解决方法
date: 2020-11-09
categories:
 - 前端笔记
tags:
 - vue
 - vue更新数据
 - forceUpdate
---

今天在做项目的时候碰到一个问题，要做的功能是这样的：把后端返回的数据通过比对id添加到vue实例的data中，所以要在原有的user对象里再添加一个addval的属性，通过遍历添加后发现页面并没有同步更新数据。

![](https://piccos.yangqifan.top/vueUpdateData1.png?imageSlim)

这是因为vue无法监听复杂的对象，直接添加属性vue是无法知道是否改变的。

解决方法有两种（这里我使用的是第一种方法）

第一种：使用vm.$forceUpdate()迫使vue实例重新渲染

第二种：使用全局API Vue.set()

![](https://piccos.yangqifan.top/vueUpdateData2.png?imageSlim)