---
title: Vuex基本用法
date: 2020-11-29
categories:
 - 前端笔记
tags:
 - Vuex
---
::: tip
Vuex是实现组件全局状态（数据）管理的一种机制，可以方便的实现组件之间数据的共享
:::
<!-- more -->
* 使用Vuex统一管理状态的好处
  1. 能够集中管理共享的数据，易与开发和维护
  2. 能够高效的实现组件之间数据共享，提高开发效率
  3. 存储在vuex中的数据是响应式的，能够实时保持数据与页面的同步
* 一般情况下，只有组件之间共享的数据，才有必要存在vuex中；对于组件中的私有数据，依旧存储在自身的data中即可

## State

* State提供唯一的公共数据源，所有共享的数据都要统一放到Store的State中进行存储

* 组件访问State中数据的第一种方式：

  ```js
  this.$store.state.全局数据名称
  ```

* 组件访问State中数据的第二种方式：

  ```js
  //1.从vuex中按需导入mapState函数
  import { mapState } from 'vuex'
  //2.将全局数据，映射为当前组件的计算属性
  computed: {
      ...mapState(['count'])
    }
  ```

## Mutation

* mutation用于变更state中的数据

* 使用mutation的第一种方式：

  ```js
  //1.定义Mutation
    mutations: {
      add (state) {
        state.count++
      }
    }
  //2.触发mutation
    methods: {
      btnHandler1 () {
        this.$store.commit('add')
      }
    }
  ```

* 触发mutation时传参

  ```js
      addN (state, step) {
        state.count += step
      }
  
  	methods: {
      btnHandler1 () {
        this.$store.commit('addN', 3)
      }
  ```

* 触发mutation第二种方式

  ```js
  //1.从vuex中按需导入mapMutations函数
  import { mapMutations } from 'vuex'
  //2.将指定的mutations函数，映射为当前组件的methods函数
   methods: {
      ...mapMutations(['sub'])
   }
  ```

## Action

* 用于处理异步任务

* 定义actions

  ```js
    actions: {
      addAsync (context) {
        setTimeout(() => {
          context.commit('add')
        }, 1000)
      }
    }
  ```

* 在actions中，不能直接修改state中的数据，必须通过context.commit()触发某个mutation才行

* 触发actions的第一种方式

  ```js
   this.$store.dispatch('addAsync')
  ```

* 第二种方式

  ```js
  //1.从vuex中按需导入mapActions函数
  import { mapActions } from 'vuex'
  //2.将刚才导入的mapActions函数，映射为当前组件的methods函数
   methods:{
       ...mapActions(['subAsync'])
   }
  ```

## Getter

* 用于对Store中的数据进行加工处理形成新的数据

* 定义Getter

  ```js
    getters: {
      showNum (state) {
        return `当前最新的数量是：【${state.count}】`
      }
    }
  ```

* 使用getters的第一种方式

  ```js
  this.$store.getters.名称
  ```

* 第二种方式

  ```js
  import { mapGetters } from 'vuex'
  
  computed: {
      ...mapGetters(['showNum'])
    }
  ```

  