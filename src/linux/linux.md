---
title: Linux与云计算
date: 2021-11-26
categories:
 - Linux
tags:
 - linux
 - 云计算
---

## 云计算的类型

### 公有云

- 公有云是作为第三方云厂商所拥有和运营的，他们通过互联网提供计算资源，在公有云中，所有的硬件、软件和其他一些基础性的结构均由云提供商拥有和运营。（阿里云、百度云、腾讯云等等）

### 私有云

- 私有云一般都是企业或者组织来使用的，可以位于企业数据中心之上，在私有云中，有专门的网络维护功能和基础结构。一般大公司都会搭建自己的私有云。

### 混合云

- 顾名思义，即同时使用公有云和私有云。从而允许公司将敏感数据保留私有云中（安全性），同时使用公有云来运行应用程序（低成本）。 

## 云计算的服务模式

云计算的主要服务模式分为3种：

- 基础设施即服务（IaaS：Infrastructure as a service）
- 平台即服务（PaaS: Platform as a Service）
- 软件即服务（SaaS: SoftWare as a Service）

### IaaS 基础设施即服务

提供按需付费的计算资源，包括服务器、网络、磁盘存储和数据中心等基础设施。

例如，AWS、Microsoft Azure、阿里云，可购买云存储，ECS服务器

### PaaS 平台即服务

提供硬件和软件工具，使开发人员更轻松地快速创建Web或移动应用。（apicloud）

###  SaaS软件即服务

基于云的应用，被授予的企业或个人通过网络访问使用。

例如，百度云盘、钉钉、53KF客服系统

## Linux命令行学习

```
# linux的命令格式：

linux命令 		一些功能性的参数		你要操作的对象
```

- linux的命令都是些单词缩写，很好记忆和理解的
- linux有两个特殊的文件夹
  - . 当前目录
  - .. 上一级目录

```
# 删除文件  rm(remove)
# 查看目录下的内容	ls(list)
# 查看当前目录	pwd(print work dir)
# 更换目录 cd(change directory)
# 移动文件与目录，或修改文件与目录的名称 mv(move file)
```

## Linux文件权限查看

- linux系统是支持多个用户，同时登录以及操作的

```
# 创建用户 useradd 用户名 
# 改用户密码 passwd 用户名
# 更改文件权限 chmod 777 filename
```
