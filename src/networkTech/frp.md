---
title: 使用frp实现内网穿透
date: 2023-07-16
categories:
 - 网络技术
tags:
 - 内网穿透
 - frp
---

## 1. 在云服务器上设置frp服务器端

- 在云服务器上下载适用于服务器端的frp二进制文件
- 解压缩frp二进制文件，并编辑`frps.ini`配置文件。
- 在配置文件中设置监听端口、认证密钥等参数。
- 启动frp服务器端。

## 2. 在内网电脑上设置frp客户端

- 在内网电脑上下载适用于客户端的frp二进制文件
- 解压缩frp二进制文件，并编辑`frpc.ini`配置文件

```
[common]
server_addr = **************  // frp服务器地址
server_port = 7000   // frp服务器端口
authentication_method = token  
token = yqf6773.  // 用于认证的令牌，与frp服务器端的配置相匹配

[tcp]
type = tcp  // 服务类型，如ssh、http、tcp等
local_ip = *************	// 本地服务的IP地址
local_port = 8025	// 本地服务的端口
remote_port = 8025 		// 远程访问该服务的端口

```

- 启动frp客户端`./frpc -c frpc.ini`